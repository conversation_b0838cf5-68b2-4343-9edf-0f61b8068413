# `input_module_my.py` 脚本说明书

## 1. 脚本概述

`input_module_my.py` 是一个用于生物信息学和计算化学的Python脚本，专门为 **Akscore2** 项目设计。其核心功能是将蛋白质（受体）和配体（小分子）的3D结构信息，从标准的化学文件格式（如 PDB, SDF, MOL2, DLG）转换为图神经网络（GNN）可以直接处理的**图数据结构**。

这个脚本是典型的**数据预处理管道 (Data Preprocessing Pipeline)**，它完成了从原始数据到机器学习模型的输入的关键一步，使得GNN能够学习和预测分子间的相互作用。

---

## 2. 依赖项

为了成功运行此脚本，你需要一个配置了以下核心库的Python环境：

### Python库:
*   **RDKit**: 用于处理和计算分子化学特征的核心库。
*   **Open Babel (`pybel`)**: 用于不同化学文件格式之间的转换。
*   **Meeko**: 用于处理 PDBQT 文件格式，这是AutoDock等对接软件常用的格式。
*   **PyTorch**: 核心的深度学习框架。
*   **PyTorch Geometric (`torch_geometric`)**: 基于PyTorch的图神经网络库，脚本最终输出的数据格式就是为其量身定做的。
*   **NumPy** & **Pandas**: 用于数值计算和数据处理。

### 外部工具 (潜在依赖):
*   脚本中部分函数调用了外部命令行工具，如 `gnss_dock2` 和 Schrodinger 套件的 `pdbconvert`。如果需要使用相关功能，必须确保这些工具已安装并配置在系统环境中。

---

## 3. 核心功能详解

### 3.1. 文件I/O与格式转换

脚本能够处理多种分子文件格式，并能进行灵活的转换。

*   **读取函数**: `Mol22mol`, `sdf2mol`, `pdb2mol`, `pdbqt2mol` 等函数利用 RDKit 和 Open Babel 将不同格式的文件读入为 RDKit 的 `Mol` 对象，这是后续所有分析的基础。
*   **多分子文件分割**: `pdb_list_cut`, `sdf_list_cut` 等函数可以将一个包含多个分子的文件（例如一个SDF文件）分割成多个独立的PDB文件，方便逐一处理。
*   **对接结果解析**: `read_pdb_block_from_pdb` 和 `poss2data` 等函数专门用于解析对接软件（如AutoDock Vina或GNINA）生成的 `.dlg` 或 `.pdbqt` 文件，能够抽取出每个对接构象（pose）的坐标、能量得分等信息。

### 3.2. 蛋白质口袋提取 (`make_pocket`)

这是至关重要的一步，旨在减少计算量并让模型聚焦于关键区域。

1.  **输入**: 整个蛋白质的PDB文件和单个配体的三维坐标。
2.  **过程**: 脚本会计算蛋白质中每个原子与配体所有原子之间的距离。
3.  **筛选**: 只有与配体最近距离小于预设阈值（`distance`，默认为5Å）的蛋白质残基（及其所有原子）会被保留下来。
4.  **输出**: 一个只包含结合口袋区域原子的新PDB数据块（字符串格式）。

### 3.3. 特征工程 (`Featurize` 类)

这是脚本的“大脑”，它定义了如何将原子和化学键翻译成GNN能理解的数字向量。

#### **节点特征 (`AtomNode`)**
为图中的每个原子（节点）生成一个包含丰富化学信息的特征向量。主要特征包括：
*   **原子类型**: One-hot编码 (C, H, O, N, S, P, ...)。
*   **拓扑信息**: 成键数（degree）、环信息（is in ring）、芳香性（is aromatic）。
*   **化学属性**: 氢原子数、价态、杂化类型 (sp, sp2, sp3)、形式电荷。
*   **物理化学属性**:
    *   **疏水性**: 对蛋白质原子，从一个预定义的字典 `prot_atom_hydrophobicity` 查询；对配体原子，则基于Gasteiger电荷判断。
    *   **药效团特征**: 通过SMARTS匹配，判断原子是否是**氢键供体**、**氢键受体**、**酸性基团**或**碱性基团**。

#### **边特征 (`CovalentEdge` & `PL_NonCovalentEdge`)**
为原子间的连接（边）生成特征向量，分为两种类型：

1.  **共价键 (Covalent Bonds)**:
    *   用于连接蛋白质内部原子，或配体内部原子。
    *   特征包括：键类型 (单键, 双键, 三键, 芳香键)、立体化学、是否在环内、是否共轭。

2.  **非共价相互作用 (Non-Covalent Interactions)**:
    *   这是脚本的亮点，用于连接蛋白质和配体原子。
    *   **相互作用类型**: 脚本编码了多种重要的分子间相互作用，包括：
        *   **疏水作用**
        *   **氢键** (区分了蛋白质作为供体和配体作为受体，反之亦然)
        *   **离子键** (盐桥，同样区分了方向)
    *   **距离信息**: 两个原子间的距离被离散化（分箱）为多个类别（例如 <2Å, <4Å, <6Å, <8Å），并进行one-hot编码。

### 3.4. 图的构建 (`mol_2_graph` 和 `make_graph`)

这些函数是整个流程的调度中心。

*   `mol_2_graph` 为单个蛋白质-配体复合物（构象）构建图。
*   `make_graph` 的逻辑更完整，用于为一个训练实例（通常对应一个PDB ID）生成一个包含多种构象的图数据列表。它能够整合：
    *   **天然构象 (Native)**: 晶体结构中的真实结合模式，RMSD被设为0。
    *   **对接构象 (Docked)**: 通过对接软件生成的构象。
    *   **诱骗分子 (Decoys/Cross)**: 作为负样本，用于提高模型的区分能力。

最终，所有特征向量和图的连接信息（`edge_index`）都被转换成`torch.Tensor`，并打包成一个 `torch_geometric.data.Data` 对象。

---

## 4. 如何运行

### 基本用法:
脚本通过命令行接收参数运行。

```bash
python input_module_my.py --input <PDB_ID> --distance <distance_in_angstrom>
```

*   `--input`: 指定要处理的PDB ID（例如 `1e66`）。脚本内部的硬编码路径会基于这个ID去查找所需文件。
*   `--distance`: 定义口袋范围的距离阈值，默认为 `5` (埃)。

### **重要提示：硬编码路径问题**

当前版本的脚本 (`input_module_my.py`) 在 `native_run` 和 `make_graph` 函数中包含了**大量写死的（hardcoded）文件路径**，例如 `/Arontier/Projects/AKscore2/...`。

这意味着，该脚本**无法在其他计算机或不同的目录结构下直接运行**。使用者必须手动修改这些路径，使其指向自己本地的数据存储位置。为了通用性，强烈建议将这些路径修改为通过命令行参数传入。

---

## 5. 输出

脚本的主要输出是保存在指定目录下的 `.pkl` 或 `.pkl.gz` 文件。每个文件包含一个或多个 `torch_geometric.data.Data` 对象。

一个典型的 `Data` 对象包含以下属性：
*   `data.x`: 节点特征矩阵，形状为 `[num_nodes, num_node_features]`。
*   `data.edge_index`: 图的连接信息（COO格式），形状为 `[2, num_edges]`。
*   `data.edge_attr`: 边的特征矩阵，形状为 `[num_edges, num_edge_features]`。
*   `data.y`: 标签数据，通常是该构象的 RMSD 值。
*   `data.bind`: 标签数据，通常是结合亲和力（binding affinity）。
*   `data.name`: 该数据点的唯一标识符。

这些文件可以直接被 Akscore2 的训练脚本加载和使用。
